{"permissions": {"allow": ["Bash(cargo check:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(timeout 60 cargo check --quiet)", "Bash(git add:*)", "Bash(git reset:*)", "Bash(git commit:*)", "Bash(cargo:*)", "Bash(RUST_BACKTRACE=1 cargo check)", "Bash(RUST_BACKTRACE=1 cargo check -p onchain-router)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(anchor build)", "Bash(RUSTFLAGS=\"-A warnings\" cargo check)", "Bash(timeout 30 cargo check)", "Bash(find:*)"], "deny": []}}