//! 循环路由实现 (套利模式)
//!
//! 处理 A -> B -> C -> A 类型的循环路由，主要用于套利操作

use anchor_lang::prelude::*;
use crate::arbitrage::{ArbitrageCalculator, ArbitrageConfig, MarketConditions, RiskManager};
use crate::error::RouteError;
use super::{types::*, executor::MultiHopRouteExecutor};

/// 循环路由执行器
pub struct CircularRouteExecutor;

impl CircularRouteExecutor {
    /// 执行循环路由（套利）
    ///
    /// # 参数
    /// * `routes` - 路由步骤列表，形成闭环
    /// * `amount_in` - 初始输入数量
    /// * `remaining_accounts` - 所有需要的账户信息
    /// * `flash_loan_config` - 闪电贷配置（可选）
    /// * `owner_seeds` - PDA签名种子（可选）
    ///
    /// # 返回
    /// 返回净利润（扣除所有费用后）
    pub fn execute<'info, 'a>(
        routes: &[Route],
        amount_in: u64,
        remaining_accounts: &'a [AccountInfo<'info>],
        flash_loan_config: Option<&FlashLoanConfig>,
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<u64> {
        msg!("开始执行循环路由（套利），步骤数: {}", routes.len());

        // 验证循环路由的有效性
        Self::validate_circular_route(routes)?;

        let initial_token = routes[0].input_mint;
        let execution_result = match flash_loan_config {
            Some(flash_config) => {
                // 使用闪电贷执行套利
                Self::execute_with_flash_loan(
                    routes,
                    amount_in,
                    remaining_accounts,
                    flash_config,
                    owner_seeds,
                )?
            }
            None => {
                // 使用自有资金执行套利
                Self::execute_with_own_funds(
                    routes,
                    amount_in,
                    remaining_accounts,
                    owner_seeds,
                )?
            }
        };

        // 验证返回代币类型正确
        let final_token = routes.last().unwrap().output_mint;
        if initial_token != final_token {
            return Err(RouteError::NotCircularRoute.into());
        }

        // 计算净利润
        let net_profit = execution_result.amount_out.checked_sub(amount_in)
            .ok_or(RouteError::ProfitCalculationError)?;

        msg!("循环路由执行完成 - 净利润: {}, Gas消耗: {}, 滑点: {}基点",
            net_profit, execution_result.gas_used, execution_result.actual_slippage_bps);

        // 验证套利成功
        if net_profit == 0 {
            msg!("警告: 套利无利润");
        } else if net_profit > 0 {
            msg!("套利成功: 利润 {} tokens", net_profit);
        } else {
            msg!("套利失败: 亏损 {} tokens", amount_in - execution_result.amount_out);
            return Err(RouteError::ProfitCalculationError.into());
        }

        Ok(net_profit)
    }

    /// 使用自有资金执行套利
    fn execute_with_own_funds<'info, 'a>(
        routes: &[Route],
        amount_in: u64,
        remaining_accounts: &'a [AccountInfo<'info>],
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<RouteResult> {
        msg!("使用自有资金执行套利，初始数量: {}", amount_in);

        // 使用通用执行器执行循环路由
        let mut result = MultiHopRouteExecutor::execute_route_sequence(
            routes,
            amount_in,
            remaining_accounts,
            owner_seeds,
        )?;

        // 设置套利利润
        let profit = result.amount_out.checked_sub(amount_in)
            .unwrap_or(0);
        result.net_profit = profit;

        msg!("自有资金套利执行完成 - 最终数量: {}, 利润: {}",
            result.amount_out, profit);

        Ok(result)
    }

    /// 增强版闪电贷套利执行（集成新的套利系统）
    pub fn execute_enhanced_flash_loan_arbitrage<'info>(
        routes: &[Route],
        flash_amount: u64,
        remaining_accounts: &'info [AccountInfo<'info>],
        flash_config: &FlashLoanConfig,
        owner_seeds: Option<&[&[&[u8]]]>,
        arbitrage_config: &ArbitrageConfig,
    ) -> Result<RouteResult> {
        msg!("开始执行增强版闪电贷套利 - 闪电贷金额: {}", flash_amount);

        // 1. 初始化套利组件
        let _calculator = ArbitrageCalculator::new(arbitrage_config.clone());
        let mut risk_manager = RiskManager::new(arbitrage_config.clone());

        // 2. 创建模拟市场条件
        let market_conditions = MarketConditions {
            network_congestion: 25,
            avg_gas_price: 100,
            volatility: 35,
            liquidity_level: 75,
        };

        // 3. 创建套利路径
        let arbitrage_path = crate::arbitrage::ArbitragePath {
            routes: routes.to_vec(),
            estimated_profit: 0, // 将被计算
            risk_score: 0,
            success_probability: 0.85,
            priority: 100,
        };

        // 4. 风险评估
        let risk_assessment = risk_manager.assess_arbitrage_risk(
            &arbitrage_path,
            flash_amount,
            &market_conditions,
        )?;

        if !risk_assessment.recommended {
            msg!("风险评估不通过 - 风险评分: {}", risk_assessment.overall_risk_score);
            return Err(RouteError::RiskScoreTooHigh.into());
        }

        // 5. 执行原有闪电贷逻辑，但增加监控
        let start_time = Clock::get()?.unix_timestamp;
        let result = Self::execute_with_flash_loan_internal(
            routes,
            flash_amount,
            remaining_accounts,
            flash_config,
            owner_seeds,
        )?;

        let execution_time_ms = (Clock::get()?.unix_timestamp - start_time) as u64 * 1000;

        // 6. 更新统计信息
        let profit = result.net_profit as i64;
        risk_manager.update_execution_stats(
            true,
            profit,
            execution_time_ms,
            flash_amount,
        );

        msg!("增强版闪电贷套利完成 - 净利润: {}, 风险评分: {}, 执行时间: {}ms",
            profit, risk_assessment.overall_risk_score, execution_time_ms);

        Ok(result)
    }

    /// 使用闪电贷执行套利
    fn execute_with_flash_loan<'info, 'a>(
        routes: &[Route],
        amount_in: u64,
        remaining_accounts: &'a [AccountInfo<'info>],
        flash_config: &FlashLoanConfig,
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<RouteResult> {
        Self::execute_with_flash_loan_internal(routes, amount_in, remaining_accounts, flash_config, owner_seeds)
    }

    /// 内部闪电贷执行逻辑
    fn execute_with_flash_loan_internal<'info, 'a>(
        routes: &[Route],
        amount_in: u64,
        remaining_accounts: &'a [AccountInfo<'info>],
        flash_config: &FlashLoanConfig,
        owner_seeds: Option<&[&[&[u8]]]>,
    ) -> Result<RouteResult> {
        msg!("使用闪电贷执行套利 - 初始数量: {}, 贷款金额: {}",
            amount_in, flash_config.amount);

        // 1. 验证闪电贷参数
        Self::validate_flash_loan_params(flash_config)?;

        // 2. 计算套利数量（原始资金 + 闪电贷）
        let flash_loan_amount = flash_config.amount;
        let arbitrage_amount = amount_in.checked_add(flash_loan_amount)
            .ok_or(RouteError::MathOverflow)?;

        msg!("闪电贷套利执行 - 总资金: {}", arbitrage_amount);

        // 3. 执行套利路径
        let mut execution_result = MultiHopRouteExecutor::execute_route_sequence(
            routes,
            arbitrage_amount,
            remaining_accounts,
            owner_seeds,
        )?;

        // 4. 计算闪电贷成本
        let flash_loan_fee = Self::calculate_flash_loan_fee(
            flash_loan_amount,
            flash_config.max_fee_bps,
        )?;
        let repayment_amount = flash_loan_amount.checked_add(flash_loan_fee)
            .ok_or(RouteError::MathOverflow)?;

        msg!("闪电贷成本 - 本金: {}, 费用: {}, 总还款: {}",
            flash_loan_amount, flash_loan_fee, repayment_amount);

        // 5. 验证能够偿还闪电贷
        if execution_result.amount_out < repayment_amount {
            msg!("闪电贷偿还失败 - 输出: {}, 需要: {}",
                execution_result.amount_out, repayment_amount);
            return Err(RouteError::FlashLoanRepaymentFailed.into());
        }

        // 6. 计算净利润（扣除闪电贷成本后）
        let net_amount = execution_result.amount_out.checked_sub(repayment_amount)
            .ok_or(RouteError::ProfitCalculationError)?;

        // 7. 再扣除初始投入，得到纯利润
        let pure_profit = net_amount.checked_sub(amount_in)
            .unwrap_or(0);

        execution_result.amount_out = net_amount;
        execution_result.net_profit = pure_profit;

        msg!("闪电贷套利执行完成 - 还款后余额: {}, 纯利润: {}",
            net_amount, pure_profit);

        Ok(execution_result)
    }

    /// 验证循环路由的有效性
    fn validate_circular_route(routes: &[Route]) -> Result<()> {
        if routes.len() < 2 {
            return Err(RouteError::InvalidRouteConfig.into());
        }

        if routes.len() > 6 {
            return Err(RouteError::RoutePathTooLong.into());
        }

        // 检查起始和结束代币是否相同
        let start_token = routes[0].input_mint;
        let end_token = routes[routes.len() - 1].output_mint;

        if start_token != end_token {
            return Err(RouteError::NotCircularRoute.into());
        }

        // 检查路径连续性
        for i in 0..routes.len() - 1 {
            if routes[i].output_mint != routes[i + 1].input_mint {
                return Err(RouteError::RouteDiscontinuity.into());
            }
        }

        Ok(())
    }

    /// 计算闪电贷费用
    fn calculate_flash_loan_fee(
        amount: u64,
        max_fee_bps: u16,
    ) -> Result<u64> {
        // 使用最大费率计算（实际应该查询当前费率）
        let fee = amount
            .checked_mul(max_fee_bps as u64)
            .ok_or(RouteError::MathOverflow)?
            .checked_div(10000)
            .ok_or(RouteError::DivisionByZero)?;

        Ok(fee)
    }

    /// 预估套利利润
    pub fn estimate_profit(
        routes: &[Route],
        amount_in: u64,
        flash_loan_config: Option<&FlashLoanConfig>,
    ) -> Result<i64> {
        msg!("估算套利利润 - 初始数量: {}", amount_in);

        // 计算实际交易数量（包括闪电贷）
        let trading_amount = if let Some(flash_config) = flash_loan_config {
            amount_in.checked_add(flash_config.amount)
                .ok_or(RouteError::MathOverflow)?
        } else {
            amount_in
        };

        // 估算执行后的输出
        let estimated_output = MultiHopRouteExecutor::estimate_total_output(routes, trading_amount)?;

        let mut total_costs = 0u64;

        // 如果使用闪电贷，计算相关费用
        if let Some(flash_config) = flash_loan_config {
            let flash_fee = Self::calculate_flash_loan_fee(
                flash_config.amount,
                flash_config.max_fee_bps,
            )?;
            let repayment = flash_config.amount.checked_add(flash_fee)
                .ok_or(RouteError::MathOverflow)?;
            total_costs = total_costs.checked_add(repayment)
                .ok_or(RouteError::MathOverflow)?;

            msg!("闪电贷成本 - 本金: {}, 费用: {}", flash_config.amount, flash_fee);
        }

        // 估算交易成本（Gas + DEX费用）
        let gas_cost = Self::estimate_gas_cost(routes);
        total_costs = total_costs.checked_add(gas_cost)
            .ok_or(RouteError::MathOverflow)?;

        // 计算净利润（扣除所有成本后）
        let profit = (estimated_output as i64)
            .checked_sub(amount_in as i64)
            .and_then(|p| p.checked_sub(total_costs as i64))
            .ok_or(RouteError::MathUnderflow)?;

        msg!("利润估算 - 预期输出: {}, 总成本: {}, 净利润: {}",
            estimated_output, total_costs, profit);

        Ok(profit)
    }

    /// 估算Gas成本
    pub fn estimate_gas_cost(routes: &[Route]) -> u64 {
        let base_cost = 35_000; // 循环路由的基础开销
        let per_step_cost = routes.iter().map(|route| {
            MultiHopRouteExecutor::estimate_step_gas(&route.dex)
        }).sum::<u64>();

        let circular_overhead = 15_000; // 额外的验证和利润计算开销

        base_cost + per_step_cost + circular_overhead
    }

    /// 检查套利机会是否值得执行
    pub fn is_profitable(
        routes: &[Route],
        amount_in: u64,
        min_profit_threshold: u64,
        flash_loan_config: Option<&FlashLoanConfig>,
    ) -> Result<bool> {
        let estimated_profit = Self::estimate_profit(routes, amount_in, flash_loan_config)?;

        msg!("套利可行性检查 - 预期利润: {}, 最小阈值: {}",
            estimated_profit, min_profit_threshold);

        // 检查是否超过最小利润阈值
        let is_profitable = estimated_profit > 0 && estimated_profit as u64 >= min_profit_threshold;

        if !is_profitable {
            if estimated_profit <= 0 {
                msg!("套利不可行: 预期亏损 {}", -estimated_profit);
            } else {
                msg!("套利不可行: 利润 {} 低于阈值 {}",
                    estimated_profit, min_profit_threshold);
            }
        }

        Ok(is_profitable)
    }

    /// 获取套利路径的复杂度评分
    pub fn get_complexity_score(routes: &[Route]) -> u8 {
        let base_score = routes.len() as u8;
        let dex_diversity_bonus = Self::count_unique_dexes(routes);

        // 复杂度 = 步骤数 + DEX多样性奖励
        base_score + dex_diversity_bonus
    }

    /// 计算路径中的唯一DEX数量
    fn count_unique_dexes(routes: &[Route]) -> u8 {
        let mut dexes = std::collections::BTreeSet::new();
        for route in routes {
            dexes.insert(&route.dex);
        }
        dexes.len() as u8
    }

    /// 验证套利路径的流动性充足性
    pub fn validate_liquidity(
        routes: &[Route],
        amount_in: u64,
    ) -> Result<()> {
        msg!("验证套利路径流动性 - 初始数量: {}", amount_in);

        // 使用通用执行器的流动性验证
        MultiHopRouteExecutor::validate_route_liquidity(routes, amount_in)?;

        // 额外的循环路由特定验证
        for (i, route) in routes.iter().enumerate() {
            if route.min_amount_out == 0 {
                msg!("警告: 步骤 {} 未设置最小输出保护", i + 1);
            }
        }

        msg!("流动性验证通过");
        Ok(())
    }

    /// 验证闪电贷参数
    fn validate_flash_loan_params(flash_config: &FlashLoanConfig) -> Result<()> {
        if flash_config.amount == 0 {
            return Err(RouteError::FlashLoanAmountExceeded.into());
        }

        if flash_config.max_fee_bps > 1000 { // 10%以上的费率不合理
            return Err(RouteError::FlashLoanRepaymentFailed.into());
        }

        Ok(())
    }

    /// 分配套利利润
    pub fn distribute_arbitrage_profit(
        net_profit: u64,
        protocol_fee_bps: u16,
        operator_fee_bps: u16,
    ) -> Result<ProfitDistribution> {
        if net_profit == 0 {
            return Ok(ProfitDistribution {
                user_profit: 0,
                protocol_profit: 0,
                operator_profit: 0,
                total_distributed: 0,
            });
        }

        // 计算协议费用
        let protocol_profit = net_profit
            .checked_mul(protocol_fee_bps as u64)
            .ok_or(RouteError::MathOverflow)?
            .checked_div(10000)
            .ok_or(RouteError::DivisionByZero)?;

        // 计算运营费用
        let operator_profit = net_profit
            .checked_mul(operator_fee_bps as u64)
            .ok_or(RouteError::MathOverflow)?
            .checked_div(10000)
            .ok_or(RouteError::DivisionByZero)?;

        // 计算用户利润
        let user_profit = net_profit
            .saturating_sub(protocol_profit)
            .saturating_sub(operator_profit);

        let total_distributed = user_profit + protocol_profit + operator_profit;

        msg!("利润分配完成 - 用户: {}, 协议: {}, 运营: {}, 总计: {}",
            user_profit, protocol_profit, operator_profit, total_distributed);

        Ok(ProfitDistribution {
            user_profit,
            protocol_profit,
            operator_profit,
            total_distributed,
        })
    }

    /// 计算套利效率指标
    pub fn calculate_arbitrage_efficiency(
        gross_profit: u64,
        total_costs: u64,
        execution_time_ms: u64,
        path_complexity: u8,
    ) -> ArbitrageEfficiency {
        let net_profit = gross_profit.saturating_sub(total_costs);

        // 利润率 = 净利润 / 总成本
        let profit_margin = if total_costs > 0 {
            (net_profit as f64 / total_costs as f64) * 100.0
        } else {
            0.0
        };

        // 执行效率 = 利润 / 执行时间
        let execution_efficiency = if execution_time_ms > 0 {
            net_profit as f64 / execution_time_ms as f64
        } else {
            0.0
        };

        // 复杂度效率 = 利润 / 路径复杂度
        let complexity_efficiency = if path_complexity > 0 {
            net_profit as f64 / path_complexity as f64
        } else {
            0.0
        };

        // 整体效率评分
        let overall_efficiency = (profit_margin + execution_efficiency + complexity_efficiency) / 3.0;

        ArbitrageEfficiency {
            profit_margin,
            execution_efficiency,
            complexity_efficiency,
            overall_efficiency,
            roi_percentage: if total_costs > 0 {
                (net_profit as f64 / total_costs as f64) * 100.0
            } else {
                0.0
            },
        }
    }
}

/// 利润分配结果
#[derive(Debug, Clone)]
pub struct ProfitDistribution {
    /// 用户分得利润
    pub user_profit: u64,
    /// 协议分得利润
    pub protocol_profit: u64,
    /// 运营方分得利润
    pub operator_profit: u64,
    /// 总分配金额
    pub total_distributed: u64,
}

/// 套利效率指标
#[derive(Debug, Clone)]
pub struct ArbitrageEfficiency {
    /// 利润率（百分比）
    pub profit_margin: f64,
    /// 执行效率（利润/时间）
    pub execution_efficiency: f64,
    /// 复杂度效率（利润/复杂度）
    pub complexity_efficiency: f64,
    /// 整体效率评分
    pub overall_efficiency: f64,
    /// 投资回报率（百分比）
    pub roi_percentage: f64,
}
